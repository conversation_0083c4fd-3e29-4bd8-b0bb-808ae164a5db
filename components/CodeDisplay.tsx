"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { Copy } from "lucide-react";

interface CodeDisplayProps {
  code: string;
  language: string;
}

export default function CodeDisplay({ code, language }: CodeDisplayProps) {
  // Copy to clipboard function
  const copyToClipboard = async () => {
    if (!code) return;

    try {
      await navigator.clipboard.writeText(code);
      toast({
        title: "Success",
        description: "Code copied to clipboard!",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy code",
        variant: "destructive",
      });
    }
  };

  if (!code) {
    return (
      <div className="border rounded-lg p-8 text-center text-gray-500 dark:text-gray-400">
        No code to display
      </div>
    );
  }

  // Split code into lines for line numbers
  const lines = code.split("\n");

  return (
    <div className="relative">
      <div className="rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
        <div className="flex">
          {/* Line numbers */}
          <div className="flex flex-col text-right text-xs text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 px-3 py-4 select-none">
            {lines.map((_, index) => (
              <div key={index} className="leading-6">
                {index + 1}
              </div>
            ))}
          </div>

          {/* Code content */}
          <div className="flex-1 overflow-x-auto">
            <pre className="text-sm leading-6 text-gray-900 dark:text-gray-100 p-4 pr-12 font-mono">
              <code>{code}</code>
            </pre>
          </div>
        </div>
      </div>

      <Button
        onClick={copyToClipboard}
        variant="ghost"
        size="sm"
        className="absolute top-2 right-2 h-8 w-8 p-0 bg-black/5 dark:bg-white/5 hover:bg-black/10 dark:hover:bg-white/10 backdrop-blur-sm"
      >
        <Copy className="h-4 w-4" />
      </Button>

      {language && (
        <div className="absolute top-2 left-16">
          <span className="px-2 py-1 bg-black/10 dark:bg-white/10 backdrop-blur-sm rounded text-xs font-medium text-gray-700 dark:text-gray-300">
            {language}
          </span>
        </div>
      )}
    </div>
  );
}
