"use client";

import React, { useState, useEffect } from "react";
import { Upload, message } from "antd";
import { InboxOutlined } from "@ant-design/icons";
import type { UploadFile, UploadProps } from "antd/es/upload";

interface ImageUploadProps {
  onFileSelect: (file: File) => void;
  onRemoveFile: () => void;
  selectedFile: File | null;
  disabled?: boolean;
}

const ACCEPTED_TYPES = ["image/png", "image/jpeg", "image/jpg", "image/webp"];
const MAX_SIZE = 5 * 1024 * 1024; // 5MB

export default function ImageUpload({
  onFileSelect,
  onRemoveFile,
  selectedFile,
  disabled = false,
}: ImageUploadProps) {
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  // Update fileList when selectedFile changes
  useEffect(() => {
    if (selectedFile) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const uploadFile: UploadFile = {
          uid: "-1",
          name: selectedFile.name,
          status: "done",
          url: e.target?.result as string,
          originFileObj: selectedFile as any, // Type assertion for compatibility
        };
        setFileList([uploadFile]);
      };
      reader.readAsDataURL(selectedFile);
    } else {
      setFileList([]);
    }
  }, [selectedFile]);

  const validateFile = (file: File): boolean => {
    if (!ACCEPTED_TYPES.includes(file.type)) {
      message.error("Please upload a valid image file (PNG, JPG, JPEG, WebP)");
      return false;
    }

    if (file.size > MAX_SIZE) {
      message.error("File size must be less than 5MB");
      return false;
    }

    return true;
  };

  const handleChange: UploadProps["onChange"] = (info) => {
    const { fileList: newFileList } = info;

    // Only keep the latest file (replace old one)
    const latestFile = newFileList[newFileList.length - 1];

    if (latestFile) {
      if (latestFile.originFileObj && validateFile(latestFile.originFileObj)) {
        onFileSelect(latestFile.originFileObj);
      }
    }
  };

  const handleRemove = () => {
    onRemoveFile();
    setFileList([]);
  };

  const handlePreview = (file: UploadFile) => {
    if (file.url) {
      // Open image in new tab
      window.open(file.url, "_blank");
    }
  };

  // Handle paste from clipboard
  useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      if (disabled) return;

      const items = e.clipboardData?.items;
      if (!items) return;

      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.indexOf("image") !== -1) {
          const file = item.getAsFile();
          if (file && validateFile(file)) {
            onFileSelect(file);
          }
          break;
        }
      }
    };

    document.addEventListener("paste", handlePaste);
    return () => document.removeEventListener("paste", handlePaste);
  }, [disabled, onFileSelect]);

  const { Dragger } = Upload;

  return (
    <div className="space-y-4">
      <Dragger
        name="file"
        multiple={false}
        maxCount={1}
        fileList={fileList}
        onChange={handleChange}
        onRemove={handleRemove}
        onPreview={handlePreview}
        beforeUpload={() => false} // Prevent auto upload
        accept={ACCEPTED_TYPES.join(",")}
        disabled={disabled}
        showUploadList={{
          showPreviewIcon: true,
          showRemoveIcon: true,
          showDownloadIcon: false,
        }}
        listType="picture-card"
        style={{
          background: "transparent",
        }}
      >
        {fileList.length === 0 && (
          <div className="space-y-4 p-4">
            <div className="flex justify-center">
              <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
                <InboxOutlined className="h-8 w-8 text-gray-600 dark:text-gray-400" />
              </div>
            </div>

            <div className="space-y-2">
              <p className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Upload your code screenshot
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Paste, drop, or click to upload
              </p>
            </div>

            <div className="flex items-center justify-center gap-4 text-xs text-gray-400 dark:text-gray-500">
              <span className="flex items-center gap-1">📷 PNG, JPG, WebP</span>
              <span>•</span>
              <span>Max 5MB</span>
            </div>
          </div>
        )}
      </Dragger>
    </div>
  );
}
