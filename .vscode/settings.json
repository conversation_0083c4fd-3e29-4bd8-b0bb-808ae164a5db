{"css.validate": false, "editor.formatOnSave": true, "editor.tabSize": 2, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "never"}, "headwind.runOnSave": false, "typescript.preferences.importModuleSpecifier": "non-relative", "eslint.validate": ["javascript", "javascriptreact", "typescript"], "typescript.tsdk": "node_modules/typescript/lib", "commentTranslate.source": "<PERSON>", "cSpell.words": ["contentlayer", "lemonsqueezy"]}