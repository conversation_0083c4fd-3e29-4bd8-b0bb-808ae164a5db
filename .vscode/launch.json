{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问：https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Typescript",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/next/dist/bin/next",
      "args": ["dev"],
      "cwd": "${workspaceFolder}",
      "skipFiles": ["<node_internals>/**"],
      "env": {
        "NODE_OPTIONS": "--inspect=9229"
      },
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "serverReadyAction": {
        "pattern": "- Local:\\s+([^\\s]+)",
        "uriFormat": "%s",
        "action": "openExternally"
      }
    },
    {
      "name": "Debug Python FastAPI",
      "type": "debugpy",
      "request": "launch",
      "module": "uvicorn",
      "args": ["backend.main:app", "--reload", "--port", "8000"],
      "jinja": true,
      "justMyCode": false,
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal"
    }
  ]
}
