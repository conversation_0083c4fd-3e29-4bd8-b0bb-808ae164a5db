"""CodeOCR API server for converting code screenshots to editable code."""

import os
from pathlib import Path

import instructor
import logfire
import uvicorn
from dotenv import load_dotenv
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from instructor.multimodal import Image
from openai import AsyncOpenAI
from pydantic import BaseModel, Field

load_dotenv(dotenv_path=".env.development", override=True)


class ImageInput(BaseModel):
    """Input model for image data supporting multiple image source formats.

    This model validates and structures the incoming image data for processing
    by the code extraction service. The source field supports various formats
    that can be automatically detected by the instructor library.

    Attributes:
        source: Image source that can be a URL, file path, or base64-encoded string.
                The instructor library's Image.autodetect() method automatically
                determines the format and processes accordingly.
    """

    source: str | Path = Field(
        ...,
        description=(
            "Image source supporting URL, file path, or base64-encoded content "
            "for code extraction."
        ),
    )


class CodeOCRResponse(BaseModel):
    """Response model containing detected programming language and extracted code.

    This model structures the output from the code extraction service, providing
    both the extracted code content and the automatically detected or specified
    programming language.

    Attributes:
        language: The detected or specified programming language of the extracted code.
        code: The code extracted from the input image.
        status: The status of the code extraction process.
    """

    language: str = Field(
        ..., description="Programming language of the extracted code."
    )
    code: str = Field(..., description="The code extracted from the image.")
    status: str = Field(
        "completed", description="Status of the code extraction process."
    )


# Initialize OpenAI client with custom configuration
openai_client = AsyncOpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=os.getenv("OPENAI_API_BASE"),
)

# Create instructor client for structured output generation
client = instructor.from_openai(
    openai_client,
    mode=instructor.Mode.JSON,
)

# Initialize FastAPI application with metadata
app = FastAPI(
    title="CodeOCR API",
    description="Convert code screenshot to editable code.",
)

# Configure CORS middleware for cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging and instrumentation for monitoring
logfire.configure(pydantic_plugin=logfire.PydanticPlugin(record="all"))
logfire.instrument_openai(openai_client)
logfire.instrument_fastapi(app)


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "CodeOCR API is running successfully!"}


@app.post("/api/v1/codeocr", response_model=CodeOCRResponse)
async def codeocr(image: ImageInput) -> CodeOCRResponse:
    """Extract code from an image using a vision model.

    This endpoint processes images containing code from various sources (URLs,
    file paths, or base64-encoded data) and uses LLM's vision capabilities to
    extract the code content and automatically detect the programming language.
    The service leverages structured output generation to ensure consistent
    response formatting.

    Args:
        image: ImageInput model containing image source data that can be a URL,
               file path, or base64-encoded string.

    Returns:
        CodeOCRResponse containing the extracted code and detected programming language.

    Raises:
        HTTPException: If the image processing fails or the model cannot extract code.
        ValidationError: If the input image format is invalid.
    """
    response = await client.chat.completions.create(
        model=os.getenv("MODEL_NAME"),
        response_model=CodeOCRResponse,
        messages=[
            {
                "role": "user",
                "content": [
                    "Extract code from this image",
                    Image.autodetect(image.source),
                ],
            },
        ],
    )
    return response


if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
